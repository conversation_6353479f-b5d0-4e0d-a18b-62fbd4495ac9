<template>
  <div class="center">
    <div v-show="twinStore.isShowVideo" class="videoBox">
      <video class="video" src="@/assets/video/loading_3d.mp4" autoplay muted loop></video>
    </div>

    <div class="wrapper-3d">
      <filterRoom v-if="nowModelName === '过滤间'" :ckData="responsiveData" @jumpScene="getModelName" />
      <cleanWaterBasin v-else-if="nowModelName === '清水池'" :ckData="responsiveData" @jumpScene="getModelName" />
      <flocculationSettlingRoom v-else-if="nowModelName === '絮凝沉淀间'" :ckData="responsiveData" @jumpScene="getModelName" />
      <waterPumpHouse v-else-if="nowModelName === '自用水泵房'" :ckData="responsiveData" @jumpScene="getModelName" />
      <chlorinationDosingRoom v-else-if="nowModelName === '加氯加药间'" :ckData="responsiveData" @jumpScene="getModelName" />
      <index v-else :ckData="responsiveData" @jumpScene="getModelName" />
    </div>

    <div class="wrapper-2d">
      <img class="vignetting" src="/src/assets/images/largeScreen/center/vignetting.png" />

      <img class="t0" src="/src/assets/images/largeScreen/center/t0.png" />
      <img class="t1" src="/src/assets/images/largeScreen/center/t1.png" />
      <img class="t2" src="/src/assets/images/largeScreen/center/t2.png" />
      <img class="t3" src="/src/assets/images/largeScreen/center/t3.png" />

      <img class="NTU" src="/src/assets/images/largeScreen/center/NTU.png" />
      <img class="PH" src="/src/assets/images/largeScreen/center/PH.png" />
      <div class="NTU">{{ NTU }}</div>
      <div class="PH">{{ PH }}</div>

      <div class="e0" ref="chart0"></div>
      <div class="e1" ref="chart1"></div>
      <div class="e2" ref="chart2"></div>
      <div class="e3" ref="chart3"></div>
      <div class="e4" ref="chart4"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import type { Ref } from 'vue';
  import LCG669WebSocket from '@/ts/LCG669WebSocket';
  import type { WebSocketData } from '/@/ts/publicData';
  import { init } from 'echarts';
  import type { ECharts } from 'echarts';
  import index from './threeJs/index.vue';
  import filterRoom from './threeJs/filterRoom.vue';
  import cleanWaterBasin from './threeJs/cleanWaterBasin.vue';
  import flocculationSettlingRoom from './threeJs/flocculationSettlingRoom.vue';
  import waterPumpHouse from './threeJs/waterPumpHouse.vue';
  import chlorinationDosingRoom from './threeJs/chlorinationDosingRoom.vue';
  import { useTwinStore } from '/@/store/modules/twin';

  const twinStore = useTwinStore();

  const nowModelName = ref('');

  const getModelName = (modelName: string) => {
    Promise.resolve().then(() => {
      // nowModelName.value = modelName;
    });
    twinStore.isShowVideo=true
  };
  const responsiveData: Ref<WebSocketData | {}> = ref({});

  const NTU = ref('-');
  const PH = ref('-');

  let lcg669WebSocket: LCG669WebSocket<WebSocketData>;

  const chart0 = ref();
  const chart1 = ref();
  const chart2 = ref();
  const chart3 = ref();
  const chart4 = ref();

  let myChart0: ECharts;
  let myChart1: ECharts;
  let myChart2: ECharts;
  let myChart3: ECharts;
  let myChart4: ECharts;

  const option0 = {
    graphic: {
      elements: [
        {
          type: 'text',
          right: '0', // 表示文本的左边界与图表容器的右边界对齐
          top: '5%', // 距离容器顶部12%的位置
          style: {
            text: '单位：m³', // 文本内容
            fill: '#FFFFFF', // 文本颜色
            fontSize: 14, // 文本字体大小
          },
        },
      ],
    },
    grid: {
      top: '20%',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      top: '3%',
      left: '0',
      data: ['1#出水', '2#出水'],
      textStyle: {
        color: '#FFFFFF', // 设置图例文本颜色为白色
      },
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF', // 设置x轴标签颜色为白色
        },
      },
      axisLine: {
        lineStyle: {
          color: '#FFFFFF', // 设置坐标轴线颜色为白色
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF', // 设置y轴标签颜色为白色
        },
      },
      splitLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        name: '1#出水',
        smooth: true,
        type: 'line',
      },
      {
        data: [],
        name: '2#出水',
        smooth: true,
        type: 'line',
      },
    ],
  };
  const option1 = {
    graphic: {
      elements: [
        {
          type: 'text',
          right: '0', // 表示文本的左边界与图表容器的右边界对齐
          top: '3%', // 距离容器顶部12%的位置
          style: {
            text: '单位：m³', // 文本内容
            fill: '#FFFFFF', // 文本颜色
            fontSize: 14, // 文本字体大小
          },
        },
      ],
    },
    grid: {
      top: '15%',
      left: '5%',
      right: '0',
      bottom: '0',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: ['今日进水', '今日出水'],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF', // 设置x轴标签颜色为白色
        },
      },
      axisLine: {
        lineStyle: {
          color: '#FFFFFF', // 设置坐标轴线颜色为白色
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF', // 设置y轴标签颜色为白色
        },
      },
      splitLine: {
        show: false,
      },
    },
    series: {
      data: [
        {
          value: '',
          itemStyle: {
            color: 'rgba(97,204,255,0.5)',
            borderColor: 'rgb(97,204,255)',
            borderWidth: 1,
            borderType: 'dashed',
            barBorderRadius: [10, 10, 0, 0],
          },
        },
        {
          value: '',
          itemStyle: {
            color: 'rgba(187,255,51,0.5)',
            borderColor: 'rgb(187,255,51)',
            borderWidth: 1,
            borderType: 'dashed',
            barBorderRadius: [10, 10, 0, 0],
          },
        },
      ],
      barWidth: '20px',
      type: 'bar',
    },
  };
  const option2 = {
    graphic: {
      elements: [
        {
          type: 'text',
          left: '41%', // 表示文本的左边界与图表容器的右边界对齐
          bottom: '8%', // 距离容器顶部12%的位置
          style: {
            text: '浊度', // 文本内容
            fill: '#FFFFFF', // 文本颜色
            fontSize: 18, // 文本字体大小
          },
        },
      ],
    },
    grid: {
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true,
    },
    series: {
      radius: '100%', //半径
      type: 'gauge',
      startAngle: 180,
      endAngle: 0,
      min: 0,
      max: 10,
      splitNumber: 5,
      backgroundStyle: {
        color: '#000', // 设置背景色
      },
      itemStyle: {
        color: '#61CCFF',
      },
      progress: {
        show: true,
        roundCap: true,
        width: 10,
      },
      // 自定义指针
      pointer: {
        icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
        length: '65%',
        width: 5,
        offsetCenter: [0, '5%'],
      },
      axisLine: {
        lineStyle: {
          color: [
            // 这个是修改仪表盘的进度条背景颜色的
            [0, '#E7F3F7'],
            [1, '#E7F3F7'],
          ],
        },
      },
      axisTick: {
        distance: 3,
        splitNumber: 2,
        lineStyle: {
          width: 2,
          color: '#FFFFFF',
        },
      },
      splitLine: {
        distance: 3,
        length: 8,
        lineStyle: {
          width: 3,
          color: '#FFFFFF',
        },
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 9,
      },
      title: {
        show: false,
      },
      detail: {
        borderColor: '#FFFFFF',
        borderWidth: 1,
        width: 80,
        height: 30,
        lineHeight: 35,
        borderRadius: 5,
        offsetCenter: [0, '35%'],
        valueAnimation: true,
        formatter: function (value) {
          return '{value|' + value + '}{unit|NTU}';
        },
        rich: {
          value: {
            fontSize: 18,
            fontWeight: '700',
            color: '#FFFFFF',
          },
          unit: {
            fontSize: 13,
            color: '#FFFFFF',
            padding: [0, 0, -3, 10],
          },
        },
      },
      data: [
        {
          value: '',
        },
      ],
    },
  };
  const option3 = {
    graphic: {
      elements: [
        {
          type: 'text',
          left: '44%', // 表示文本的左边界与图表容器的右边界对齐
          bottom: '8%', // 距离容器顶部12%的位置
          style: {
            text: 'PH', // 文本内容
            fill: '#FFFFFF', // 文本颜色
            fontSize: 18, // 文本字体大小
          },
        },
      ],
    },
    grid: {
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true,
    },
    series: {
      radius: '100%', //半径
      type: 'gauge',
      startAngle: 180,
      endAngle: 0,
      min: 0,
      max: 14,
      splitNumber: 7,
      backgroundStyle: {
        color: '#000', // 设置背景色
      },
      itemStyle: {
        color: '#BBFF33',
      },
      progress: {
        show: true,
        roundCap: true,
        width: 10,
      },
      // 自定义指针
      pointer: {
        icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
        length: '65%',
        width: 5,
        offsetCenter: [0, '5%'],
      },
      axisLine: {
        lineStyle: {
          color: [
            // 这个是修改仪表盘的进度条背景颜色的
            [0, '#E7F3F7'],
            [1, '#E7F3F7'],
          ],
        },
      },
      axisTick: {
        distance: 3,
        splitNumber: 2,
        lineStyle: {
          width: 2,
          color: '#FFFFFF',
        },
      },
      splitLine: {
        distance: 3,
        length: 8,
        lineStyle: {
          width: 3,
          color: '#FFFFFF',
        },
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 9,
      },
      title: {
        show: false,
      },
      detail: {
        borderColor: '#FFFFFF',
        borderWidth: 1,
        width: 80,
        height: 30,
        lineHeight: 35,
        borderRadius: 5,
        offsetCenter: [0, '35%'],
        valueAnimation: true,
        formatter: function (value) {
          return '{value|' + value + '}';
        },
        rich: {
          value: {
            fontSize: 18,
            fontWeight: '700',
            color: '#FFFFFF',
          },
        },
      },
      data: [
        {
          value: '',
        },
      ],
    },
  };
  const option4 = {
    graphic: {
      elements: [
        {
          type: 'text',
          left: '41%', // 表示文本的左边界与图表容器的右边界对齐
          bottom: '8%', // 距离容器顶部12%的位置
          style: {
            text: '余氯', // 文本内容
            fill: '#FFFFFF', // 文本颜色
            fontSize: 18, // 文本字体大小
          },
        },
      ],
    },
    grid: {
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true,
    },
    series: {
      radius: '100%', //半径
      type: 'gauge',
      startAngle: 180,
      endAngle: 0,
      min: 0,
      max: 5,
      splitNumber: 5,
      backgroundStyle: {
        color: '#000', // 设置背景色
      },
      itemStyle: {
        color: '#F8CE68',
      },
      progress: {
        show: true,
        roundCap: true,
        width: 10,
      },
      // 自定义指针
      pointer: {
        icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
        length: '65%',
        width: 5,
        offsetCenter: [0, '5%'],
      },
      axisLine: {
        lineStyle: {
          color: [
            // 这个是修改仪表盘的进度条背景颜色的
            [0, '#E7F3F7'],
            [1, '#E7F3F7'],
          ],
        },
      },
      axisTick: {
        distance: 3,
        splitNumber: 2,
        lineStyle: {
          width: 2,
          color: '#FFFFFF',
        },
      },
      splitLine: {
        distance: 3,
        length: 8,
        lineStyle: {
          width: 3,
          color: '#FFFFFF',
        },
      },
      axisLabel: {
        color: '#FFFFFF',
        fontSize: 9,
      },
      title: {
        show: false,
      },
      detail: {
        borderColor: '#FFFFFF',
        borderWidth: 1,
        width: 80,
        height: 30,
        lineHeight: 35,
        borderRadius: 5,
        offsetCenter: [0, '35%'],
        valueAnimation: true,
        formatter: function (value) {
          return '{value|' + value + '}{unit|mg/L}';
        },
        rich: {
          value: {
            fontSize: 18,
            fontWeight: '700',
            color: '#FFFFFF',
          },
          unit: {
            fontSize: 13,
            color: '#FFFFFF',
            padding: [0, 0, -3, 10],
          },
        },
      },
      data: [
        {
          value: '',
        },
      ],
    },
  };

  /**
   * echarts图表 数据最大条数
   */
  const maxLength = 4;

  const assemblyData = () => {
    // console.log('数字孪生返回数据：', lcg669WebSocket.ckData)
    responsiveData.value = lcg669WebSocket.ckData;

    option0.series[0].data.push(lcg669WebSocket.ckData.index3.data);
    option0.series[1].data.push(lcg669WebSocket.ckData.index4.data);
    option0.xAxis.data.push(lcg669WebSocket.ckData.recentTime.split(' ')[1]);
    if (option0.series[0].data.length > maxLength) {
      option0.series[0].data.shift();
      option0.series[1].data.shift();
      option0.xAxis.data.shift();
    }

    option1.series.data[0].value = lcg669WebSocket.ckData.index5.data;
    option1.series.data[1].value = lcg669WebSocket.ckData.index6.data;

    option2.series.data[0].value = lcg669WebSocket.ckData.index9.data;
    option3.series.data[0].value = lcg669WebSocket.ckData.index10.data;
    option4.series.data[0].value = lcg669WebSocket.ckData.index11.data;

    myChart0.setOption(option0, {
      notMerge: true,
      replaceMerge: ['series'],
    });
    myChart1.setOption(option1);
    myChart2.setOption(option2);
    myChart3.setOption(option3);
    myChart4.setOption(option4);

    NTU.value = lcg669WebSocket.ckData.index7.data;
    PH.value = lcg669WebSocket.ckData.index8.data;
  };

  onMounted(() => {
    lcg669WebSocket = new LCG669WebSocket('ztWebsocket', 'TWIN001' + '/web', assemblyData);

    myChart0 = init(chart0.value);
    myChart1 = init(chart1.value);
    myChart2 = init(chart2.value);
    myChart3 = init(chart3.value);
    myChart4 = init(chart4.value);
  });

  onUnmounted(() => {
    lcg669WebSocket.closeWebSocket();
  });
</script>

<style lang="less" scoped>
  .center {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;

    .wrapper-3d {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
    }

    .wrapper-2d {
      pointer-events: none;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
    }

    .videoBox {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      background-color: #2e5d91;

      .video {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .vignetting {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none; /* 使鼠标事件穿透 */
      user-select: none;
    }

    .t0,
    .t1,
    .t2,
    .t3 {
      width: 320px;
      height: 50px;
      position: absolute;
      pointer-events: all;
    }

    .t0 {
      top: 150px;
      left: 30px;
    }

    .t1 {
      top: 500px;
      left: 30px;
    }

    .t2 {
      top: 150px;
      right: 30px;
    }

    .t3 {
      top: 360px;
      right: 30px;
    }

    .NTU,
    .PH {
      position: absolute;
      width: 100px;
      height: 130px;
      top: 220px;
      text-align: center;
      line-height: 100px;
    }

    .NTU {
      right: 220px;
      color: #f8ce68;
    }

    .PH {
      right: 70px;
      color: #bbff33;
    }

    .e0,.e1,.e2,.e3,.e4{
      pointer-events: all;
    }

    .e0 {
      position: absolute;
      width: 320px;
      height: 260px;
      top: 210px;
      left: 30px;
    }

    .e1 {
      position: absolute;
      width: 320px;
      height: 400px;
      top: 560px;
      left: 30px;
    }

    .e2 {
      position: absolute;
      width: 200px;
      height: 200px;
      top: 430px;
      right: 90px;
    }

    .e3 {
      position: absolute;
      width: 200px;
      height: 200px;
      top: 630px;
      right: 90px;
    }

    .e4 {
      position: absolute;
      width: 200px;
      height: 200px;
      top: 830px;
      right: 90px;
    }
  }
</style>

<style lang="less">
  .threeJsDataFrame {
    background-color: rgba(255, 255, 255, 0.8) !important;

    .name {
      color: black !important;
    }
  }

  .topCS {
    position: absolute;
    top: -9999px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px;
    background-color: white;
    z-index: 999999999999;
  }

  .backIndex {
    width: 116px;
    height: 30px;
    position: absolute;
    top: 250px;
    right: 450px;
    cursor: pointer;
  }

  .titleName {
    font-weight: 700;
    font-size: 36px;
    color: #ffffff;
    line-height: 52px;
    position: absolute;
    top: 180px;
    left: 50%;
    transform: translateX(-50%);
  }

  .legend {
    position: absolute;
    bottom: 68px;
    right: 340px;
    color: white;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);

    .legend-item {
      cursor: pointer;
      display: flex;
      gap: 4px;
    }

    .legend-item:hover {
      filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
    }
  }
</style>
