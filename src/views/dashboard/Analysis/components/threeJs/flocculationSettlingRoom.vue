// 絮凝沉淀间 flocculationSettlingRoom

<template>
  <div ref="containerRef" class="flocculationSettlingRoom">
    <canvas
      class="threejs3DCanvas"
      ref="threejs3DCanvas"
      @mousedown="clickRayCaster ? clickRayCaster.down($event) : undefined"
      @mouseup="clickRayCaster ? clickRayCaster.up($event) : undefined"
    ></canvas>

    <img class="backIndex" src="/src/assets/images/largeScreen/center/backIndex.png" @click="jumpScene('index')" />

    <YSXK-DataFrame
      class="threeJsDataFrame"
      v-for="(item, i) in aData"
      :key="i"
      :title="item.title"
      :position="item.position"
      :style="{ transform: item.position.transform }"
      :columnData="item.columnData"
      v-show="isShow(item.modelName)"
    />

    <div class="titleName">热荣数字孪生水厂-絮凝沉淀间</div>

    <!--    图例：用于控制 数据检测、电子围栏的显隐-->
    <div class="legend">
      <div @click="isShowLegend(item)"  class="legend-item" v-for="(item,index) in items" :key="index">
        <a-icon :color="item.isShow ? 'green' : 'gray'" :type="item.type"></a-icon>
        <div>{{item.name}}</div>
      </div>
    </div>

    <!-- 需要保留该测试代码，用以触发DOM更新 -->
    <div class="topCS">{{ topCS }}</div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import * as THREE from 'three';
  import type { GLTF } from 'three/examples/jsm/loaders/GLTFLoader.js';
  import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

  // 导入分离的模块
  import BaseLight from './ts/BaseLight';
  import CameraConfig from './ts/CameraConfig';
  import type { CameraConfigData } from './ts/CameraConfig';
  import ControlsConfig from './ts/ControlsConfig';
  import type { ControlsConfigData } from './ts/ControlsConfig';
  import RendererConfig from './ts/RendererConfig';
  import ClosePage from './ts/ClosePage';
  import { ClickRayCaster } from './ts/RayCaster';
  import { InitLabel, InitMeter } from './ts/AddLabel';

  import { useHomePage } from '/@/store/modules/homePage';

  import type { WebSocketData } from '/@/ts/publicData';
  import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
  import { useTwinStore } from '@/store/modules/twin';

  const emit = defineEmits(['jumpScene']);

  function jumpScene(modelName: string) {
    twinStore.isShowVideo = true;
    emit('jumpScene', modelName);
  }

  interface IndexProps {
    ckData: WebSocketData;
  }

  const props = defineProps<IndexProps>();

  const aData = ref();

  const containerRef = ref(null);

  watch(
    () => props.ckData,
    (newData, _oldData) => {
      aData.value = [
        {
          // 标题
          title: '1#沉淀池',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '排泥状态', // 标题
              data: newData.index25.data, // 值
              unit: newData.index25.unit, // 单位
              flag: newData.index25.warningFlag, // 是否报警
              type: 'SludgeDischargeState', // 数据类型
            },
          ],
          modelName: '1#沉淀池排泥',
        },
        {
          // 标题
          title: '2#沉淀池',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '排泥状态', // 标题
              data: newData.index26.data, // 值
              unit: newData.index26.unit, // 单位
              flag: newData.index26.warningFlag, // 是否报警
              type: 'SludgeDischargeState', // 数据类型
            },
          ],
          modelName: '2#沉淀池排泥',
        },
        {
          // 标题
          title: '1号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index27.data, // 值
              unit: newData.index27.unit, // 单位
              flag: newData.index27.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀',
        },
        {
          // 标题
          title: '2号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index28.data, // 值
              unit: newData.index28.unit, // 单位
              flag: newData.index28.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀001',
        },
        {
          // 标题
          title: '3号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index29.data, // 值
              unit: newData.index29.unit, // 单位
              flag: newData.index29.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀002',
        },
        {
          // 标题
          title: '4号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index30.data, // 值
              unit: newData.index30.unit, // 单位
              flag: newData.index30.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀003',
        },
        {
          // 标题
          title: '5号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index31.data, // 值
              unit: newData.index31.unit, // 单位
              flag: newData.index31.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀004',
        },
        {
          // 标题
          title: '10号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index32.data, // 值
              unit: newData.index32.unit, // 单位
              flag: newData.index32.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀005',
        },
        {
          // 标题
          title: '9号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index33.data, // 值
              unit: newData.index33.unit, // 单位
              flag: newData.index33.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀006',
        },
        {
          // 标题
          title: '8号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index34.data, // 值
              unit: newData.index34.unit, // 单位
              flag: newData.index34.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀007',
        },
        {
          // 标题
          title: '7号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index35.data, // 值
              unit: newData.index35.unit, // 单位
              flag: newData.index35.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀008',
        },
        {
          // 标题
          title: '6号电磁阀',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '状态', // 标题
              data: newData.index36.data, // 值
              unit: newData.index36.unit, // 单位
              flag: newData.index36.warningFlag, // 是否报警
              type: 'Hoist', // 数据类型
            },
          ],
          modelName: '排泥电磁阀009',
        },
        {
          // 标题
          title: '进水浊度计',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: '浊度', // 标题
              data: newData.index37.data, // 值
              unit: newData.index37.unit, // 单位
              flag: newData.index37.warningFlag, // 是否报警
              type: '', // 数据类型
            },
          ],
          modelName: '控制箱',
        },
        {
          // 标题
          title: '进水PH计',
          // 数据框位置
          position: {
            top: '669px',
            left: '669px',
          },
          // 每一条数据
          columnData: [
            {
              name: 'PH', // 标题
              data: newData.index38.data, // 值
              unit: newData.index38.unit, // 单位
              flag: newData.index38.warningFlag, // 是否报警
              type: '', // 数据类型
            },
          ],
          modelName: '控制箱001',
        },
      ];
    }
  );

  const isShow = (modelName: string) => {
    if (aData.value && clickRayCaster && clickRayCaster.nowModel) {
      return modelName === clickRayCaster.nowModel.userData.modelName;
    } else {
      return false;
    }
  };

  const topNum = ref(0);
  const topCS = ref('空');

  const topTest = () => {
    topNum.value++;
    if (clickRayCaster.nowModel && clickRayCaster.nowModel.userData.modelName) {
      topCS.value = clickRayCaster.nowModel.userData.modelName + topNum.value;
    } else {
      topCS.value = '没有' + topNum.value;
    }
  };

  const homePage = useHomePage();

  let camera: THREE.PerspectiveCamera;

  /**
   * GLTF模型加载器
   */
  const gltfLoader = new GLTFLoader();

  const dracoLoader = new DRACOLoader();
  dracoLoader.setDecoderPath('/threejs/libs/draco/');
  gltfLoader.setDRACOLoader(dracoLoader);

  /**
   * vue3虚拟Dom节点 以ref和变量名进行自动关联
   */
  const threejs3DCanvas = ref<HTMLCanvasElement>();

  /**
   * 渲染器
   */
  let renderer: THREE.WebGLRenderer;

  /**
   * 轨道控制器 阻尼相机
   */
  let controls: OrbitControls;

  /**
   * 创建画布
   */
  const scene = new THREE.Scene();

  const tickIdArr: number[] = [];

  const isRun = ref(true);

  const items = ref([
    {
      name: '数据检测',
      type: 'check-circle',
      isShow: true,
    }
  ])

  const isShowLegend = (item) => {
    if (twinStore.isShowVideo) return;
    const {isShow,type} = item;
    item.isShow = !isShow;
    scene.traverse(i=>{
      if(i.userData.type === type){
        i.visible = !i.visible;
      }
    })
  };

  /**
   * 不可交互标签，纯显示，按名称
   */
  const labelsOnlyShowArr = ['1#沉淀池', '2#沉淀池', '配水井'];
  // 可交互标签，按名称，统一设备图标
  const meterAll = [];

  /**
   * 可交互标签
   */
  const labels: THREE.Group = new THREE.Group();
  labels.name = 'labels';
  /**
   * 不可交互标签，纯显示
   */
  const labelsOnlyShow: THREE.Group = new THREE.Group();
  labelsOnlyShow.name = 'labelsOnlyShow';
  // 可交互标签，设备组
  let meterAllModel: THREE.Object3D<THREE.Object3DEventMap>;

  /**
   * 鼠标左键单击射线检测
   */
  let clickRayCaster: ClickRayCaster;

  /**
   * 生成模型标签，纯显示
   */
  const getLabelOnlyShow = (model: THREE.Group) => {
    // 创建标签
    const initLabel = new InitLabel(model, labelsOnlyShowArr);

    labelsOnlyShow.add(initLabel.labels);
  };
  /**
   * 生成统一设备模型标签，meter.png
   */
  const getMeter = (model: THREE.Group) => {
    meterAllModel = model.getObjectByName('点击显示数据框');
    // 获取组内所有子级模型名称
    meterAllModel.children.forEach((item) => {
      meterAll.push(item.name);
    });
    const initMeter = new InitMeter(model, meterAll,'check-circle');

    labels.add(initMeter.labels);
  };

  /**
   * 将生成的模型标签，添加进入场景中
   */
  const addLabel = () => {
    scene.add(labels);
    scene.add(labelsOnlyShow);
  };

  const resizeFun = () => {
    // 使用 nextTick 确保 DOM 更新完成后再获取尺寸
    nextTick(() => {
      if (containerRef.value && camera && renderer) {
        camera.aspect = containerRef.value.clientWidth / containerRef.value.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(containerRef.value.clientWidth, containerRef.value.clientHeight);
      }
    });
  };

  const twinStore = useTwinStore();

  const world = async (tickBackFun: Function) => {
    twinStore.isShowVideo = true;

    const controlsConfigData: ControlsConfigData = {
      target: new THREE.Vector3(0, 5, 0),
      minDistance: 0.1,
      maxDistance: 20,
    };
    controls = ControlsConfig(camera, threejs3DCanvas.value, controlsConfigData);

    // 挂载
    renderer = new THREE.WebGLRenderer({
      canvas: threejs3DCanvas.value,
      powerPreference: 'high-performance',
      antialias: true, // 抗锯齿 如果 renderer 要使用必须在这里打开，后处理模式下无效
      alpha: false,
    });

    // render 配置
    RendererConfig(containerRef.value.clientWidth, containerRef.value.clientHeight, renderer);

    // ！！！注意不要随意更改模块运行顺序
    BaseLight(scene, renderer);
    const cameraConfigData: CameraConfigData = {
      position: new THREE.Vector3(3.3314892352895944, 16.04709400226316, -9.11081383002448),
    };
    CameraConfig(camera, cameraConfigData);

    // 响应式更新
    window.addEventListener('resize', resizeFun);

    /**
     * 打开阴影的投射与接收
     */
    const openShadow = (model: THREE.Group) => {
      model.traverse((child) => {
        if (child.name !== '科技围墙') {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });
    };
    /**
     * 自发光贴图
     */
    const openLight = (model: THREE.Group) => {
      // @ts-ignore
      const scienceAndTechnologyFence = model.getObjectByName('科技围墙') as THREE.Mesh<
        THREE.BufferGeometry<THREE.NormalBufferAttributes>,
        THREE.MeshStandardMaterial
      >;
      // scienceAndTechnologyFence.material.lightMap = scienceAndTechnologyFence.material.map
      // scienceAndTechnologyFence.material.lightMapIntensity = .669
      scienceAndTechnologyFence.material.emissive = new THREE.Color(0xffffff);
      scienceAndTechnologyFence.material.emissiveIntensity = 2;
      scienceAndTechnologyFence.material.metalness = 0;
      scienceAndTechnologyFence.material.roughness = 1;

      // const renderScene = (model: THREE.Mesh<THREE.BufferGeometry<THREE.NormalBufferAttributes>, THREE.MeshStandardMaterial>) => {
      //     // 使用requestAnimationFrame函数进行渲染
      //     model.material.map.offset.x -= .0075

      //     tickIdArr[1] = window.requestAnimationFrame(() => {
      //         renderScene(model)
      //     })
      // }
      // renderScene(scienceAndTechnologyFence)
    };

    /**
     * 每帧回调处理
     */
    const tick = () => {
      // 更新阻尼相机轨道动画
      controls.update();

      // 更新FPS显示器
      // stats.update()

      // 更新摄像机坐标
      camera.updateProjectionMatrix();

      renderer.render(scene, camera);

      // 下一帧执行
      tickIdArr[0] = window.requestAnimationFrame(tick);

      // console.log(camera.position)

      tickBackFun();
    };

    const initModel = (gltf: GLTF) => {
      getLabelOnlyShow(gltf.scene);
      getMeter(gltf.scene);
      addLabel();

      // 开始鼠标单击射线检测
      clickRayCaster = new ClickRayCaster(
        camera,
        labels,
        // 单击回调事件
        () => {
          topTest();
        },
        // 传入 canvas 元素引用
        threejs3DCanvas.value
      );

      if (isRun.value) {
        scene.add(gltf.scene.clone());
        // 开始渲染
        tick();
      }
    };

    return new Promise<void>((resolve) => {
      if (homePage.flocculationSettlingRoomGltf) {
        initModel(homePage.flocculationSettlingRoomGltf);
        resolve();
      } else {
        /**
         * 导入模型
         */
        gltfLoader.load('/threejs/models/flocculationSettlingRoom-v1.glb', (gltf) => {
          openShadow(gltf.scene);
          openLight(gltf.scene);

          homePage.setFlocculationSettlingRoomModel(gltf);

          initModel(homePage.flocculationSettlingRoomGltf);

          resolve();
        });
      }
    });
  };

  /**
   * 计算 监测点 div 的位置
   */
  const calculateData = () => {
    if (props.ckData && aData.value && meterAllModel && containerRef.value) {
      const w = containerRef.value.clientWidth / 2;
      const h = containerRef.value.clientHeight / 2;

      meterAllModel.children.forEach((item, _i) => {
        const objP3D = new THREE.Vector3();

        item.getWorldPosition(objP3D);

        // 判断是否在视野内
        const objP2D = objP3D.project(camera);

        aData.value.forEach((aDataItem) => {
          if (aDataItem.modelName === item.userData.modelName && isShow(aDataItem.modelName)) {
            // 只有在视野内且需要显示时才更新位置
            // 计算像素位置
            const topPx = -objP2D.y * h + h;
            const leftPx = objP2D.x * w + w;
            // 使用transform替代top/left
            aDataItem.position = {
              transform: `translate(${leftPx}px, ${topPx}px)`,
              top: '0px',
              left: '0px',
            };
          }
        });
      });
    }
  };

  onMounted(() => {
    camera = new THREE.PerspectiveCamera(75, containerRef.value.clientWidth / containerRef.value.clientHeight, 0.1, 1000);

    //初始化场景
    world(calculateData).then(() => {
      twinStore.isShowVideo = false;
    });
  });

  onUnmounted(() => {
    isRun.value = false;
    ClosePage(scene, renderer, resizeFun, tickIdArr);
  });
</script>

<style lang="less" scoped>
  .flocculationSettlingRoom {
    width: 100%;
    height: 100%;
    position: relative;

    .threejs3DCanvas {
      width: 100%;
      height: 100%;
    }

    .threeJsDataFrame {
      position: absolute;
      will-change: transform;
    }
  }
</style>
